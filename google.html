<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Tab</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', arial, sans-serif;
            background: #202124;
            color: #e8eaed;
            height: 100vh;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            position: relative;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .header-left a {
            color: #e8eaed;
            text-decoration: none;
            font-size: 13px;
            opacity: 0.8;
            transition: opacity 0.2s;
        }

        .header-left a:hover {
            opacity: 1;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-right a {
            color: #e8eaed;
            text-decoration: none;
            font-size: 13px;
            opacity: 0.8;
            transition: opacity 0.2s;
        }

        .header-right a:hover {
            opacity: 1;
        }

        .apps-menu {
            width: 24px;
            height: 24px;
            cursor: pointer;
            opacity: 0.8;
            transition: opacity 0.2s;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e8eaed"><path d="M6,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM16,6c0,1.1 0.9,2 2,2s2,-0.9 2,-2 -0.9,-2 -2,-2 -2,0.9 -2,2zM12,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2z"/></svg>') center/contain no-repeat;
        }

        .apps-menu:hover {
            opacity: 1;
        }

        .profile-pic {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            cursor: pointer;
            background: linear-gradient(45deg, #4285f4, #34a853, #fbbc05, #ea4335);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 500;
            font-size: 14px;
        }

        /* Main Content */
        .main-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: calc(100vh - 200px);
            padding: 0 20px;
        }

        .google-logo {
            font-size: 90px;
            font-weight: 400;
            margin-bottom: 35px;
            letter-spacing: -5px;
        }

        .google-logo .g1 { color: #4285f4; }
        .google-logo .o1 { color: #ea4335; }
        .google-logo .o2 { color: #fbbc05; }
        .google-logo .g2 { color: #4285f4; }
        .google-logo .l { color: #34a853; }
        .google-logo .e { color: #ea4335; }

        /* Search Container */
        .search-container {
            width: 100%;
            max-width: 584px;
            position: relative;
            margin-bottom: 35px;
        }

        .search-box {
            width: 100%;
            height: 44px;
            border: 1px solid #5f6368;
            border-radius: 24px;
            background: #303134;
            color: #e8eaed;
            font-size: 16px;
            padding: 0 45px 0 45px;
            outline: none;
            transition: all 0.2s;
        }

        .search-box:hover {
            border-color: #8ab4f8;
            box-shadow: 0 1px 6px rgba(32,33,36,.28);
        }

        .search-box:focus {
            border-color: #8ab4f8;
            box-shadow: 0 1px 6px rgba(32,33,36,.28);
        }

        .search-icon {
            position: absolute;
            left: 14px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            opacity: 0.6;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%239aa0a6"><path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/></svg>') center/contain no-repeat;
        }

        .voice-search {
            position: absolute;
            right: 45px;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            cursor: pointer;
            opacity: 0.6;
            transition: opacity 0.2s;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234285f4"><path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z"/></svg>') center/contain no-repeat;
        }

        .voice-search:hover {
            opacity: 1;
        }

        .image-search {
            position: absolute;
            right: 14px;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            cursor: pointer;
            opacity: 0.6;
            transition: opacity 0.2s;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234285f4"><path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/></svg>') center/contain no-repeat;
        }

        .image-search:hover {
            opacity: 1;
        }

        /* Search Buttons */
        .search-buttons {
            display: flex;
            gap: 14px;
            margin-bottom: 30px;
        }

        .search-btn {
            background: #303134;
            border: 1px solid #303134;
            border-radius: 4px;
            color: #e8eaed;
            font-size: 14px;
            padding: 10px 20px;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-block;
        }

        .search-btn:hover {
            border-color: #5f6368;
            box-shadow: 0 1px 1px rgba(0,0,0,.1);
        }

        /* Apps Menu Dropdown */
        .apps-menu-dropdown {
            position: absolute;
            top: 60px;
            right: 60px;
            width: 320px;
            background: #2d2e30;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.3);
            padding: 20px;
            display: none;
            z-index: 1000;
        }

        .apps-menu-dropdown.show {
            display: block;
        }

        .apps-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }

        .app-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.2s;
            text-decoration: none;
            color: #e8eaed;
        }

        .app-item:hover {
            background-color: #3c4043;
        }

        .app-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            background: #4285f4;
        }

        .app-title {
            font-size: 12px;
            text-align: center;
            color: #e8eaed;
        }

        /* Profile Menu Dropdown */
        .profile-menu-dropdown {
            position: absolute;
            top: 60px;
            right: 20px;
            width: 320px;
            background: #2d2e30;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.3);
            padding: 20px;
            display: none;
            z-index: 1000;
        }

        .profile-menu-dropdown.show {
            display: block;
        }

        .profile-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 20px;
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #4285f4, #34a853, #fbbc05, #ea4335);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: 500;
            margin-bottom: 12px;
        }

        .profile-name {
            font-size: 16px;
            font-weight: 500;
            color: #e8eaed;
            margin-bottom: 4px;
        }

        .profile-email {
            font-size: 14px;
            color: #9aa0a6;
        }

        .manage-account-btn {
            width: 100%;
            background: transparent;
            border: 1px solid #5f6368;
            border-radius: 20px;
            color: #8ab4f8;
            padding: 8px 16px;
            font-size: 14px;
            cursor: pointer;
            margin: 16px 0;
            transition: all 0.2s;
        }

        .manage-account-btn:hover {
            background: #3c4043;
        }

        .profile-actions {
            display: flex;
            justify-content: space-between;
            border-top: 1px solid #3c4043;
            padding-top: 16px;
        }

        .profile-action {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #e8eaed;
            text-decoration: none;
            font-size: 14px;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .profile-action:hover {
            background-color: #3c4043;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .google-logo {
                font-size: 60px;
                margin-bottom: 25px;
            }

            .search-container {
                max-width: 90%;
            }

            .shortcuts-grid {
                grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
                gap: 15px;
            }

            .header {
                padding: 10px 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-left">
            <a href="#" onclick="showAbout()">About</a>
            <a href="#" onclick="showStore()">Store</a>
        </div>
        <div class="header-right">
            <a href="#" onclick="openGmail()">Gmail</a>
            <a href="#" onclick="openImages()">Images</a>
            <div class="apps-menu" onclick="toggleAppsMenu()" title="Google apps"></div>
            <div class="profile-pic" onclick="toggleProfileMenu()" title="Google Account">U</div>
        </div>

        <!-- Apps Menu Dropdown -->
        <div class="apps-menu-dropdown" id="appsMenuDropdown">
            <div class="apps-grid">
                <a href="https://myaccount.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background: #1a73e8;">👤</div>
                    <div class="app-title">Account</div>
                </a>
                <a href="https://www.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background: #4285f4;">🔍</div>
                    <div class="app-title">Search</div>
                </a>
                <a href="https://maps.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background: #34a853;">🗺️</div>
                    <div class="app-title">Maps</div>
                </a>
                <a href="https://gemini.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background: #9aa0a6;">✨</div>
                    <div class="app-title">Gemini</div>
                </a>
                <a href="https://news.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background: #4285f4;">📰</div>
                    <div class="app-title">News</div>
                </a>
                <a href="https://gmail.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background: #ea4335;">📧</div>
                    <div class="app-title">Gmail</div>
                </a>
                <a href="https://meet.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background: #00832d;">📹</div>
                    <div class="app-title">Meet</div>
                </a>
                <a href="https://chat.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background: #00ac47;">💬</div>
                    <div class="app-title">Chat</div>
                </a>
                <a href="https://contacts.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background: #4285f4;">👥</div>
                    <div class="app-title">Contacts</div>
                </a>
                <a href="https://calendar.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background: #4285f4;">📅</div>
                    <div class="app-title">Calendar</div>
                </a>
                <a href="https://play.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background: #01875f;">▶️</div>
                    <div class="app-title">Play</div>
                </a>
                <a href="https://translate.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background: #4285f4;">🌐</div>
                    <div class="app-title">Translate</div>
                </a>
            </div>
        </div>

        <!-- Profile Menu Dropdown -->
        <div class="profile-menu-dropdown" id="profileMenuDropdown">
            <div class="profile-header">
                <div class="profile-avatar">U</div>
                <div class="profile-name">Hi, Tanvir Ahmed!</div>
                <div class="profile-email"><EMAIL></div>
            </div>
            <button class="manage-account-btn" onclick="openAccountManagement()">
                Manage your Google Account
            </button>
            <div class="profile-actions">
                <a href="#" class="profile-action" onclick="addAccount()">
                    <span>➕</span>
                    <span>Add account</span>
                </a>
                <a href="#" class="profile-action" onclick="signOut()">
                    <span>🚪</span>
                    <span>Sign out</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Google Logo -->
        <div class="google-logo">
            <span class="g1">G</span><span class="o1">o</span><span class="o2">o</span><span class="g2">g</span><span class="l">l</span><span class="e">e</span>
        </div>

        <!-- Search Container -->
        <div class="search-container">
            <div class="search-icon"></div>
            <input type="text" class="search-box" id="searchInput" placeholder="" autocomplete="off">
            <div class="voice-search" onclick="startVoiceSearch()" title="Search by voice"></div>
            <div class="image-search" onclick="openImageSearch()" title="Search by image"></div>
        </div>

        <!-- Search Buttons -->
        <div class="search-buttons">
            <button class="search-btn" onclick="googleSearch()">Google Search</button>
            <button class="search-btn" onclick="feelingLucky()">I'm Feeling Lucky</button>
        </div>
    </div>

    <script>
        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            setupSearchInput();
            setRandomBackground();
            setupClickOutside();
        });

        // Setup search input functionality
        function setupSearchInput() {
            const searchInput = document.getElementById('searchInput');

            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    googleSearch();
                }
            });

            // Focus on search input when page loads
            searchInput.focus();
        }

        // Search functions
        function googleSearch() {
            const query = document.getElementById('searchInput').value;
            if (query.trim()) {
                window.open(`https://www.google.com/search?q=${encodeURIComponent(query)}`, '_blank');
            }
        }

        function feelingLucky() {
            const query = document.getElementById('searchInput').value;
            if (query.trim()) {
                window.open(`https://www.google.com/search?q=${encodeURIComponent(query)}&btnI=1`, '_blank');
            } else {
                window.open('https://www.google.com/doodles', '_blank');
            }
        }

        // Voice search functionality
        function startVoiceSearch() {
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                const recognition = new SpeechRecognition();

                recognition.continuous = false;
                recognition.interimResults = false;
                recognition.lang = 'en-US';

                recognition.onstart = function() {
                    document.getElementById('searchInput').placeholder = 'Listening...';
                };

                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    document.getElementById('searchInput').value = transcript;
                    document.getElementById('searchInput').placeholder = '';
                    googleSearch();
                };

                recognition.onerror = function(event) {
                    document.getElementById('searchInput').placeholder = '';
                    alert('Voice search error: ' + event.error);
                };

                recognition.onend = function() {
                    document.getElementById('searchInput').placeholder = '';
                };

                recognition.start();
            } else {
                alert('Voice search is not supported in this browser.');
            }
        }

        // Image search functionality
        function openImageSearch() {
            window.open('https://images.google.com', '_blank');
        }

        // Header functions
        function showAbout() {
            window.open('https://about.google', '_blank');
        }

        function showStore() {
            window.open('https://store.google.com', '_blank');
        }

        function openGmail() {
            window.open('https://gmail.com', '_blank');
        }

        function openImages() {
            window.open('https://images.google.com', '_blank');
        }

        function toggleAppsMenu() {
            const dropdown = document.getElementById('appsMenuDropdown');
            const profileDropdown = document.getElementById('profileMenuDropdown');

            // Close profile menu if open
            profileDropdown.classList.remove('show');

            // Toggle apps menu
            dropdown.classList.toggle('show');
        }

        function toggleProfileMenu() {
            const dropdown = document.getElementById('profileMenuDropdown');
            const appsDropdown = document.getElementById('appsMenuDropdown');

            // Close apps menu if open
            appsDropdown.classList.remove('show');

            // Toggle profile menu
            dropdown.classList.toggle('show');
        }

        // Setup click outside to close dropdowns
        function setupClickOutside() {
            document.addEventListener('click', function(event) {
                const appsMenu = document.querySelector('.apps-menu');
                const profilePic = document.querySelector('.profile-pic');
                const appsDropdown = document.getElementById('appsMenuDropdown');
                const profileDropdown = document.getElementById('profileMenuDropdown');

                // Close apps dropdown if clicking outside
                if (!appsMenu.contains(event.target) && !appsDropdown.contains(event.target)) {
                    appsDropdown.classList.remove('show');
                }

                // Close profile dropdown if clicking outside
                if (!profilePic.contains(event.target) && !profileDropdown.contains(event.target)) {
                    profileDropdown.classList.remove('show');
                }
            });
        }

        // Profile menu functions
        function openAccountManagement() {
            window.open('https://myaccount.google.com', '_blank');
        }

        function addAccount() {
            window.open('https://accounts.google.com/signin/v2/identifier?continue=https%3A%2F%2Fmyaccount.google.com', '_blank');
        }

        function signOut() {
            if (confirm('Are you sure you want to sign out?')) {
                window.open('https://accounts.google.com/logout', '_blank');
            }
        }

        // Set random background (subtle)
        function setRandomBackground() {
            const backgrounds = [
                'linear-gradient(135deg, #202124 0%, #1a1a1d 100%)',
                'linear-gradient(135deg, #1e1e1e 0%, #2d2d30 100%)',
                'linear-gradient(135deg, #212121 0%, #1c1c1c 100%)'
            ];

            const randomBg = backgrounds[Math.floor(Math.random() * backgrounds.length)];
            document.body.style.background = randomBg;
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + K to focus search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                document.getElementById('searchInput').focus();
            }

            // Escape to clear search
            if (e.key === 'Escape') {
                document.getElementById('searchInput').value = '';
                document.getElementById('searchInput').focus();
            }
        });
    </script>
</body>
</html>