<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Tab</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', arial, sans-serif;
            background: #202124;
            color: #e8eaed;
            height: 100vh;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            position: relative;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .header-left a {
            color: #e8eaed;
            text-decoration: none;
            font-size: 13px;
            opacity: 0.8;
            transition: opacity 0.2s;
        }

        .header-left a:hover {
            opacity: 1;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-right a {
            color: #e8eaed;
            text-decoration: none;
            font-size: 13px;
            opacity: 0.8;
            transition: opacity 0.2s;
        }

        .header-right a:hover {
            opacity: 1;
        }

        .apps-menu {
            width: 24px;
            height: 24px;
            cursor: pointer;
            opacity: 0.8;
            transition: opacity 0.2s;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e8eaed"><path d="M6,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM16,6c0,1.1 0.9,2 2,2s2,-0.9 2,-2 -0.9,-2 -2,-2 -2,0.9 -2,2zM12,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2z"/></svg>') center/contain no-repeat;
        }

        .apps-menu:hover {
            opacity: 1;
        }

        .profile-pic {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            cursor: pointer;
            background: linear-gradient(45deg, #4285f4, #34a853, #fbbc05, #ea4335);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 500;
            font-size: 14px;
        }

        /* Main Content */
        .main-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: calc(100vh - 200px);
            padding: 0 20px;
        }

        .google-logo {
            font-size: 90px;
            font-weight: 400;
            margin-bottom: 35px;
            letter-spacing: -5px;
        }

        .google-logo .g1 { color: #4285f4; }
        .google-logo .o1 { color: #ea4335; }
        .google-logo .o2 { color: #fbbc05; }
        .google-logo .g2 { color: #4285f4; }
        .google-logo .l { color: #34a853; }
        .google-logo .e { color: #ea4335; }

        /* Search Container */
        .search-container {
            width: 100%;
            max-width: 584px;
            position: relative;
            margin-bottom: 35px;
        }

        .search-box {
            width: 100%;
            height: 44px;
            border: 1px solid #5f6368;
            border-radius: 24px;
            background: #303134;
            color: #e8eaed;
            font-size: 16px;
            padding: 0 45px 0 45px;
            outline: none;
            transition: all 0.2s;
        }

        .search-box:hover {
            border-color: #8ab4f8;
            box-shadow: 0 1px 6px rgba(32,33,36,.28);
        }

        .search-box:focus {
            border-color: #8ab4f8;
            box-shadow: 0 1px 6px rgba(32,33,36,.28);
        }

        .search-icon {
            position: absolute;
            left: 14px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            opacity: 0.6;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%239aa0a6"><path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/></svg>') center/contain no-repeat;
        }

        .voice-search {
            position: absolute;
            right: 45px;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            cursor: pointer;
            opacity: 0.6;
            transition: opacity 0.2s;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234285f4"><path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z"/></svg>') center/contain no-repeat;
        }

        .voice-search:hover {
            opacity: 1;
        }

        .image-search {
            position: absolute;
            right: 14px;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            cursor: pointer;
            opacity: 0.6;
            transition: opacity 0.2s;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234285f4"><path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/></svg>') center/contain no-repeat;
        }

        .image-search:hover {
            opacity: 1;
        }

        /* Search Buttons */
        .search-buttons {
            display: flex;
            gap: 14px;
            margin-bottom: 30px;
        }

        .search-btn {
            background: #303134;
            border: 1px solid #303134;
            border-radius: 4px;
            color: #e8eaed;
            font-size: 14px;
            padding: 10px 20px;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-block;
        }

        .search-btn:hover {
            border-color: #5f6368;
            box-shadow: 0 1px 1px rgba(0,0,0,.1);
        }

        /* Shortcuts Section */
        .shortcuts-section {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .shortcuts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(112px, 1fr));
            gap: 20px;
            max-width: 672px;
            margin: 0 auto;
        }

        .shortcut-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #e8eaed;
            padding: 12px;
            border-radius: 8px;
            transition: background-color 0.2s;
            cursor: pointer;
        }

        .shortcut-item:hover {
            background-color: #303134;
        }

        .shortcut-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            background: #303134;
        }

        .shortcut-title {
            font-size: 12px;
            text-align: center;
            max-width: 80px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .google-logo {
                font-size: 60px;
                margin-bottom: 25px;
            }

            .search-container {
                max-width: 90%;
            }

            .shortcuts-grid {
                grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
                gap: 15px;
            }

            .header {
                padding: 10px 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-left">
            <a href="#" onclick="showAbout()">About</a>
            <a href="#" onclick="showStore()">Store</a>
        </div>
        <div class="header-right">
            <a href="#" onclick="openGmail()">Gmail</a>
            <a href="#" onclick="openImages()">Images</a>
            <div class="apps-menu" onclick="toggleAppsMenu()" title="Google apps"></div>
            <div class="profile-pic" onclick="toggleProfileMenu()" title="Google Account">U</div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Google Logo -->
        <div class="google-logo">
            <span class="g1">G</span><span class="o1">o</span><span class="o2">o</span><span class="g2">g</span><span class="l">l</span><span class="e">e</span>
        </div>

        <!-- Search Container -->
        <div class="search-container">
            <div class="search-icon"></div>
            <input type="text" class="search-box" id="searchInput" placeholder="" autocomplete="off">
            <div class="voice-search" onclick="startVoiceSearch()" title="Search by voice"></div>
            <div class="image-search" onclick="openImageSearch()" title="Search by image"></div>
        </div>

        <!-- Search Buttons -->
        <div class="search-buttons">
            <button class="search-btn" onclick="googleSearch()">Google Search</button>
            <button class="search-btn" onclick="feelingLucky()">I'm Feeling Lucky</button>
        </div>
    </div>

    <!-- Shortcuts Section -->
    <div class="shortcuts-section">
        <div class="shortcuts-grid" id="shortcutsGrid">
            <!-- Shortcuts will be dynamically generated -->
        </div>
    </div>

    <script>
        // Default shortcuts data
        const defaultShortcuts = [
            { title: 'YouTube', url: 'https://youtube.com', icon: '📺', color: '#ff0000' },
            { title: 'Gmail', url: 'https://gmail.com', icon: '📧', color: '#ea4335' },
            { title: 'Google Drive', url: 'https://drive.google.com', icon: '💾', color: '#4285f4' },
            { title: 'Google Maps', url: 'https://maps.google.com', icon: '🗺️', color: '#34a853' },
            { title: 'Google Photos', url: 'https://photos.google.com', icon: '📷', color: '#fbbc05' },
            { title: 'Google Calendar', url: 'https://calendar.google.com', icon: '📅', color: '#4285f4' },
            { title: 'Google Docs', url: 'https://docs.google.com', icon: '📄', color: '#4285f4' },
            { title: 'Google Sheets', url: 'https://sheets.google.com', icon: '📊', color: '#34a853' },
            { title: 'Google Slides', url: 'https://slides.google.com', icon: '📽️', color: '#fbbc05' },
            { title: 'Google News', url: 'https://news.google.com', icon: '📰', color: '#4285f4' },
            { title: 'Google Translate', url: 'https://translate.google.com', icon: '🌐', color: '#4285f4' },
            { title: 'Google Keep', url: 'https://keep.google.com', icon: '📝', color: '#fbbc05' }
        ];

        // Load shortcuts from localStorage or use defaults
        let shortcuts = JSON.parse(localStorage.getItem('chromeShortcuts')) || defaultShortcuts;

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            renderShortcuts();
            setupSearchInput();
            setRandomBackground();
        });

        // Render shortcuts
        function renderShortcuts() {
            const grid = document.getElementById('shortcutsGrid');
            grid.innerHTML = '';

            shortcuts.forEach((shortcut, index) => {
                const shortcutElement = document.createElement('div');
                shortcutElement.className = 'shortcut-item';
                shortcutElement.onclick = () => window.open(shortcut.url, '_blank');

                shortcutElement.innerHTML = `
                    <div class="shortcut-icon" style="background-color: ${shortcut.color}">
                        ${shortcut.icon}
                    </div>
                    <div class="shortcut-title">${shortcut.title}</div>
                `;

                grid.appendChild(shortcutElement);
            });
        }

        // Setup search input functionality
        function setupSearchInput() {
            const searchInput = document.getElementById('searchInput');

            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    googleSearch();
                }
            });

            // Focus on search input when page loads
            searchInput.focus();
        }

        // Search functions
        function googleSearch() {
            const query = document.getElementById('searchInput').value;
            if (query.trim()) {
                window.open(`https://www.google.com/search?q=${encodeURIComponent(query)}`, '_blank');
            }
        }

        function feelingLucky() {
            const query = document.getElementById('searchInput').value;
            if (query.trim()) {
                window.open(`https://www.google.com/search?q=${encodeURIComponent(query)}&btnI=1`, '_blank');
            } else {
                window.open('https://www.google.com/doodles', '_blank');
            }
        }

        // Voice search functionality
        function startVoiceSearch() {
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                const recognition = new SpeechRecognition();

                recognition.continuous = false;
                recognition.interimResults = false;
                recognition.lang = 'en-US';

                recognition.onstart = function() {
                    document.getElementById('searchInput').placeholder = 'Listening...';
                };

                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    document.getElementById('searchInput').value = transcript;
                    document.getElementById('searchInput').placeholder = '';
                    googleSearch();
                };

                recognition.onerror = function(event) {
                    document.getElementById('searchInput').placeholder = '';
                    alert('Voice search error: ' + event.error);
                };

                recognition.onend = function() {
                    document.getElementById('searchInput').placeholder = '';
                };

                recognition.start();
            } else {
                alert('Voice search is not supported in this browser.');
            }
        }

        // Image search functionality
        function openImageSearch() {
            window.open('https://images.google.com', '_blank');
        }

        // Header functions
        function showAbout() {
            window.open('https://about.google', '_blank');
        }

        function showStore() {
            window.open('https://store.google.com', '_blank');
        }

        function openGmail() {
            window.open('https://gmail.com', '_blank');
        }

        function openImages() {
            window.open('https://images.google.com', '_blank');
        }

        function toggleAppsMenu() {
            // In a real implementation, this would show a dropdown menu
            alert('Apps menu - would show Google apps grid');
        }

        function toggleProfileMenu() {
            // In a real implementation, this would show profile options
            alert('Profile menu - would show account options');
        }

        // Set random background (subtle)
        function setRandomBackground() {
            const backgrounds = [
                'linear-gradient(135deg, #202124 0%, #1a1a1d 100%)',
                'linear-gradient(135deg, #1e1e1e 0%, #2d2d30 100%)',
                'linear-gradient(135deg, #212121 0%, #1c1c1c 100%)'
            ];

            const randomBg = backgrounds[Math.floor(Math.random() * backgrounds.length)];
            document.body.style.background = randomBg;
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + K to focus search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                document.getElementById('searchInput').focus();
            }

            // Escape to clear search
            if (e.key === 'Escape') {
                document.getElementById('searchInput').value = '';
                document.getElementById('searchInput').focus();
            }
        });
    </script>
</body>
</html>