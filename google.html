<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Tab</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        @import url('https://fonts.googleapis.com/css2?family=Product+Sans:wght@400;500&display=swap');

        :root {
            --bg-color: #202124;
            --text-color: #e8eaed;
            --text-secondary: #9aa0a6;
            --border-color: #5f6368;
            --hover-bg: #303134;
            --search-bg: #303134;
            --modal-bg: #2d2e30;
            --google-text-color: #e8eaed;
        }

        [data-theme="light"] {
            --bg-color: #ffffff;
            --text-color: #3c4043;
            --text-secondary: #5f6368;
            --border-color: #dadce0;
            --hover-bg: #f8f9fa;
            --search-bg: #ffffff;
            --modal-bg: #ffffff;
            --google-text-color: #3c4043;
        }

        body {
            font-family: 'Product Sans', 'Roboto', arial, sans-serif;
            background: var(--bg-color);
            color: var(--text-color);
            height: 100vh;
            overflow-x: hidden;
            transition: all 0.3s ease;
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            position: relative;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .header-left a {
            color: var(--text-color);
            text-decoration: none;
            font-size: 13px;
            opacity: 0.8;
            transition: opacity 0.2s;
        }

        .header-left a:hover {
            opacity: 1;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-right a {
            color: var(--text-color);
            text-decoration: none;
            font-size: 13px;
            opacity: 0.8;
            transition: opacity 0.2s;
        }

        .header-right a:hover {
            opacity: 1;
        }

        .apps-menu {
            width: 24px;
            height: 24px;
            cursor: pointer;
            opacity: 0.8;
            transition: opacity 0.2s;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e8eaed"><path d="M6,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM16,6c0,1.1 0.9,2 2,2s2,-0.9 2,-2 -0.9,-2 -2,-2 -2,0.9 -2,2zM12,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2z"/></svg>') center/contain no-repeat;
        }

        .apps-menu:hover {
            opacity: 1;
        }

        .profile-pic {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            cursor: pointer;
            background: linear-gradient(45deg, #4285f4, #34a853, #fbbc05, #ea4335);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 500;
            font-size: 14px;
        }

        /* Main Content */
        .main-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: calc(100vh - 200px);
            padding: 0 20px;
        }

        .google-logo {
            font-size: 90px;
            font-weight: 400;
            margin-bottom: 35px;
            letter-spacing: -5px;
            color: var(--google-text-color);
        }

        /* Search Container */
        .search-container {
            width: 100%;
            max-width: 584px;
            position: relative;
            margin-bottom: 35px;
        }

        .search-box {
            width: 100%;
            height: 44px;
            border: 1px solid var(--border-color);
            border-radius: 24px;
            background: var(--search-bg);
            color: var(--text-color);
            font-size: 16px;
            padding: 0 45px 0 45px;
            outline: none;
            transition: all 0.2s;
        }

        .search-box:hover {
            border-color: #8ab4f8;
            box-shadow: 0 1px 6px rgba(32,33,36,.28);
        }

        .search-box:focus {
            border-color: #8ab4f8;
            box-shadow: 0 1px 6px rgba(32,33,36,.28);
        }

        .search-icon {
            position: absolute;
            left: 14px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            opacity: 0.6;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%239aa0a6"><path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/></svg>') center/contain no-repeat;
        }

        .voice-search {
            position: absolute;
            right: 45px;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            cursor: pointer;
            opacity: 0.6;
            transition: opacity 0.2s;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234285f4"><path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z"/></svg>') center/contain no-repeat;
        }

        .voice-search:hover {
            opacity: 1;
        }

        .image-search {
            position: absolute;
            right: 14px;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            cursor: pointer;
            opacity: 0.6;
            transition: opacity 0.2s;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234285f4"><path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/></svg>') center/contain no-repeat;
        }

        .image-search:hover {
            opacity: 1;
        }

        /* Search Buttons */
        .search-buttons {
            display: flex;
            gap: 14px;
            margin-bottom: 30px;
        }

        .search-btn {
            background: #303134;
            border: 1px solid #303134;
            border-radius: 4px;
            color: #e8eaed;
            font-size: 14px;
            padding: 10px 20px;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-block;
        }

        .search-btn:hover {
            border-color: #5f6368;
            box-shadow: 0 1px 1px rgba(0,0,0,.1);
        }

        /* Apps Menu Dropdown */
        .apps-menu-dropdown {
            position: absolute;
            top: 60px;
            right: 60px;
            width: 320px;
            background: #2d2e30;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.3);
            padding: 20px;
            display: none;
            z-index: 1000;
        }

        .apps-menu-dropdown.show {
            display: block;
        }

        .apps-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }

        .app-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.2s;
            text-decoration: none;
            color: #e8eaed;
        }

        .app-item:hover {
            background-color: #3c4043;
        }

        .app-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            background: #4285f4;
        }

        .app-title {
            font-size: 12px;
            text-align: center;
            color: #e8eaed;
        }

        /* Profile Menu Dropdown */
        .profile-menu-dropdown {
            position: absolute;
            top: 60px;
            right: 20px;
            width: 320px;
            background: #2d2e30;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.3);
            padding: 20px;
            display: none;
            z-index: 1000;
        }

        .profile-menu-dropdown.show {
            display: block;
        }

        .profile-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 20px;
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #4285f4, #34a853, #fbbc05, #ea4335);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: 500;
            margin-bottom: 12px;
        }

        .profile-name {
            font-size: 16px;
            font-weight: 500;
            color: #e8eaed;
            margin-bottom: 4px;
        }

        .profile-email {
            font-size: 14px;
            color: #9aa0a6;
        }

        .manage-account-btn {
            width: 100%;
            background: transparent;
            border: 1px solid #5f6368;
            border-radius: 20px;
            color: #8ab4f8;
            padding: 8px 16px;
            font-size: 14px;
            cursor: pointer;
            margin: 16px 0;
            transition: all 0.2s;
        }

        .manage-account-btn:hover {
            background: #3c4043;
        }

        .profile-actions {
            display: flex;
            justify-content: space-between;
            border-top: 1px solid #3c4043;
            padding-top: 16px;
        }

        .profile-action {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #e8eaed;
            text-decoration: none;
            font-size: 14px;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .profile-action:hover {
            background-color: #3c4043;
        }

        /* Shortcuts Section */
        .shortcuts-section {
            width: 100%;
            max-width: 1200px;
            margin: 40px auto 0;
            padding: 0 20px;
        }

        .shortcuts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(112px, 1fr));
            gap: 20px;
            max-width: 672px;
            margin: 0 auto;
        }

        .shortcut-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: var(--text-color);
            padding: 12px;
            border-radius: 8px;
            transition: background-color 0.2s;
            cursor: pointer;
            position: relative;
        }

        .shortcut-item:hover {
            background-color: var(--hover-bg);
        }

        .shortcut-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            background: var(--hover-bg);
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .shortcut-title {
            font-size: 12px;
            text-align: center;
            max-width: 80px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .add-shortcut {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px;
            border-radius: 8px;
            transition: all 0.2s;
            cursor: pointer;
            border: 1px solid var(--border-color);
            opacity: 0.6;
        }

        .add-shortcut:hover {
            background-color: var(--hover-bg);
            opacity: 1;
            transform: scale(1.02);
        }

        .add-shortcut-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: 300;
            color: var(--text-secondary);
            background: transparent;
        }

        .add-shortcut-title {
            font-size: 12px;
            text-align: center;
            color: var(--text-secondary);
        }

        .shortcut-remove {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: #5f6368;
            border-radius: 50%;
            display: none;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 12px;
            color: white;
        }

        .shortcut-item:hover .shortcut-remove {
            display: flex;
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: var(--modal-bg);
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 400px;
            max-width: 90%;
            border: 1px solid var(--border-color);
        }

        .modal-header {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 20px;
            color: var(--text-color);
        }

        .modal-input {
            width: 100%;
            padding: 12px;
            margin-bottom: 15px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--search-bg);
            color: var(--text-color);
            font-size: 14px;
        }

        .modal-input:focus {
            outline: none;
            border-color: #8ab4f8;
        }

        .modal-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .modal-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .modal-btn-cancel {
            background: transparent;
            color: #8ab4f8;
        }

        .modal-btn-save {
            background: #8ab4f8;
            color: #202124;
        }

        /* Theme Toggle */
        .theme-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--hover-bg);
            border: 1px solid var(--border-color);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .theme-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .theme-icon {
            transition: all 0.3s ease;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .google-logo {
                font-size: 60px;
                margin-bottom: 25px;
            }

            .search-container {
                max-width: 90%;
            }

            .shortcuts-grid {
                grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
                gap: 15px;
            }

            .header {
                padding: 10px 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-left">
            <a href="#" onclick="showAbout()">About</a>
            <a href="#" onclick="showStore()">Store</a>
        </div>
        <div class="header-right">
            <a href="#" onclick="openGmail()">Gmail</a>
            <a href="#" onclick="openImages()">Images</a>
            <div class="apps-menu" onclick="toggleAppsMenu()" title="Google apps"></div>
            <div class="profile-pic" onclick="toggleProfileMenu()" title="Google Account">U</div>
        </div>

        <!-- Apps Menu Dropdown -->
        <div class="apps-menu-dropdown" id="appsMenuDropdown">
            <div class="apps-grid">
                <a href="https://myaccount.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 24 24%22><circle cx=%2212%22 cy=%228%22 r=%223%22 fill=%22%23fff%22/><path d=%22M12 14c-4 0-6 2-6 4v2h12v-2c0-2-2-4-6-4z%22 fill=%22%23fff%22/></svg>'); background-color: #1a73e8;"></div>
                    <div class="app-title">Account</div>
                </a>
                <a href="https://www.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 24 24%22><path d=%22M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z%22 fill=%22%234285F4%22/><path d=%22M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z%22 fill=%22%2334A853%22/><path d=%22M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z%22 fill=%22%23FBBC05%22/><path d=%22M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z%22 fill=%22%23EA4335%22/></svg>'); background-color: #fff;"></div>
                    <div class="app-title">Search</div>
                </a>
                <a href="https://maps.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 24 24%22><path d=%22M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z%22 fill=%22%23EA4335%22/></svg>'); background-color: #34A853;"></div>
                    <div class="app-title">Maps</div>
                </a>
                <a href="https://gemini.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background: linear-gradient(45deg, #4285F4, #34A853, #FBBC05, #EA4335); color: white; font-size: 20px;">✨</div>
                    <div class="app-title">Gemini</div>
                </a>
                <a href="https://news.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 24 24%22><path d=%22M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z%22 fill=%22%23fff%22/></svg>'); background-color: #4285F4;"></div>
                    <div class="app-title">News</div>
                </a>
                <a href="https://gmail.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 24 24%22><path d=%22M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z%22 fill=%22%23EA4335%22/></svg>'); background-color: #fff;"></div>
                    <div class="app-title">Gmail</div>
                </a>
                <a href="https://meet.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 24 24%22><path d=%22M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z%22 fill=%22%2300832D%22/></svg>'); background-color: #fff;"></div>
                    <div class="app-title">Meet</div>
                </a>
                <a href="https://chat.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 24 24%22><path d=%22M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4v3c0 .6.4 1 1 1 .2 0 .5-.1.7-.3L14.6 18H20c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z%22 fill=%22%2300AC47%22/></svg>'); background-color: #fff;"></div>
                    <div class="app-title">Chat</div>
                </a>
                <a href="https://contacts.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 24 24%22><path d=%22M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z%22 fill=%22%234285F4%22/></svg>'); background-color: #fff;"></div>
                    <div class="app-title">Contacts</div>
                </a>
                <a href="https://calendar.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 24 24%22><path d=%22M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z%22 fill=%22%234285F4%22/></svg>'); background-color: #fff;"></div>
                    <div class="app-title">Calendar</div>
                </a>
                <a href="https://play.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background: linear-gradient(45deg, #01875F, #4285F4, #EA4335, #FBBC05); color: white; font-size: 20px;">▶</div>
                    <div class="app-title">Play</div>
                </a>
                <a href="https://translate.google.com" class="app-item" target="_blank">
                    <div class="app-icon" style="background-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 24 24%22><path d=%22M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z%22 fill=%22%234285F4%22/></svg>'); background-color: #fff;"></div>
                    <div class="app-title">Translate</div>
                </a>
            </div>
        </div>

        <!-- Profile Menu Dropdown -->
        <div class="profile-menu-dropdown" id="profileMenuDropdown">
            <div class="profile-header">
                <div class="profile-avatar">U</div>
                <div class="profile-name">Hi, Tanvir Ahmed!</div>
                <div class="profile-email"><EMAIL></div>
            </div>
            <button class="manage-account-btn" onclick="openAccountManagement()">
                Manage your Google Account
            </button>
            <div class="profile-actions">
                <a href="#" class="profile-action" onclick="addAccount()">
                    <span>➕</span>
                    <span>Add account</span>
                </a>
                <a href="#" class="profile-action" onclick="signOut()">
                    <span>🚪</span>
                    <span>Sign out</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Google Logo -->
        <div class="google-logo">Google</div>

        <!-- Search Container -->
        <div class="search-container">
            <div class="search-icon"></div>
            <input type="text" class="search-box" id="searchInput" placeholder="" autocomplete="off">
            <div class="voice-search" onclick="startVoiceSearch()" title="Search by voice"></div>
            <div class="image-search" onclick="openImageSearch()" title="Search by image"></div>
        </div>

        <!-- Search Buttons -->
        <div class="search-buttons">
            <button class="search-btn" onclick="googleSearch()">Google Search</button>
            <button class="search-btn" onclick="feelingLucky()">I'm Feeling Lucky</button>
        </div>
    </div>

    <!-- Shortcuts Section -->
    <div class="shortcuts-section">
        <div class="shortcuts-grid" id="shortcutsGrid">
            <!-- Shortcuts will be dynamically generated -->
        </div>
    </div>

    <!-- Add Shortcut Modal -->
    <div id="addShortcutModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">Add shortcut</div>
            <input type="text" id="shortcutName" class="modal-input" placeholder="Name" maxlength="50">
            <input type="url" id="shortcutUrl" class="modal-input" placeholder="URL">
            <div class="modal-buttons">
                <button class="modal-btn modal-btn-cancel" onclick="closeAddShortcutModal()">Cancel</button>
                <button class="modal-btn modal-btn-save" onclick="saveShortcut()">Done</button>
            </div>
        </div>
    </div>

    <!-- Theme Toggle -->
    <div class="theme-toggle" onclick="toggleTheme()" title="Toggle theme">
        <span class="theme-icon" id="themeIcon">🌙</span>
    </div>

    <script>
        // Load shortcuts from localStorage or use empty array
        let shortcuts = JSON.parse(localStorage.getItem('chromeShortcuts')) || [];

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            setupSearchInput();
            setRandomBackground();
            setupClickOutside();
            renderShortcuts();
            initializeTheme();
        });

        // Theme management
        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'dark';
            const themeIcon = document.getElementById('themeIcon');

            if (savedTheme === 'light') {
                document.documentElement.setAttribute('data-theme', 'light');
                themeIcon.textContent = '🌙';
            } else {
                document.documentElement.removeAttribute('data-theme');
                themeIcon.textContent = '☀️';
            }
        }

        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const themeIcon = document.getElementById('themeIcon');

            if (currentTheme === 'light') {
                // Switch to dark
                document.documentElement.removeAttribute('data-theme');
                themeIcon.textContent = '☀️';
                localStorage.setItem('theme', 'dark');
            } else {
                // Switch to light
                document.documentElement.setAttribute('data-theme', 'light');
                themeIcon.textContent = '🌙';
                localStorage.setItem('theme', 'light');
            }
        }

        // Render shortcuts
        function renderShortcuts() {
            const grid = document.getElementById('shortcutsGrid');
            grid.innerHTML = '';

            // Add existing shortcuts
            shortcuts.forEach((shortcut, index) => {
                const shortcutElement = document.createElement('div');
                shortcutElement.className = 'shortcut-item';
                shortcutElement.onclick = (e) => {
                    if (!e.target.classList.contains('shortcut-remove')) {
                        window.open(shortcut.url, '_blank');
                    }
                };

                const faviconUrl = getFaviconUrl(shortcut.url);

                shortcutElement.innerHTML = `
                    <div class="shortcut-remove" onclick="removeShortcut(${index})" title="Remove shortcut">×</div>
                    <div class="shortcut-icon" style="background-image: url('${faviconUrl}'); background-color: #303134;">
                        ${shortcut.title.charAt(0).toUpperCase()}
                    </div>
                    <div class="shortcut-title">${shortcut.title}</div>
                `;

                grid.appendChild(shortcutElement);
            });

            // Add "+" button for adding new shortcuts
            const addElement = document.createElement('div');
            addElement.className = 'add-shortcut';
            addElement.onclick = openAddShortcutModal;

            addElement.innerHTML = `
                <div class="add-shortcut-icon">+</div>
                <div class="add-shortcut-title">Add shortcut</div>
            `;

            grid.appendChild(addElement);
        }

        // Get favicon URL for a website
        function getFaviconUrl(url) {
            try {
                const domain = new URL(url).hostname;
                return `https://www.google.com/s2/favicons?domain=${domain}&sz=64`;
            } catch (e) {
                return '';
            }
        }

        // Open add shortcut modal
        function openAddShortcutModal() {
            document.getElementById('addShortcutModal').style.display = 'block';
            document.getElementById('shortcutName').focus();
        }

        // Close add shortcut modal
        function closeAddShortcutModal() {
            document.getElementById('addShortcutModal').style.display = 'none';
            document.getElementById('shortcutName').value = '';
            document.getElementById('shortcutUrl').value = '';
        }

        // Save new shortcut
        function saveShortcut() {
            const name = document.getElementById('shortcutName').value.trim();
            const url = document.getElementById('shortcutUrl').value.trim();

            if (!name || !url) {
                alert('Please enter both name and URL');
                return;
            }

            // Add http:// if no protocol specified
            let finalUrl = url;
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                finalUrl = 'https://' + url;
            }

            // Add new shortcut
            shortcuts.push({
                title: name,
                url: finalUrl
            });

            // Save to localStorage
            localStorage.setItem('chromeShortcuts', JSON.stringify(shortcuts));

            // Re-render shortcuts
            renderShortcuts();

            // Close modal
            closeAddShortcutModal();
        }

        // Remove shortcut
        function removeShortcut(index) {
            shortcuts.splice(index, 1);
            localStorage.setItem('chromeShortcuts', JSON.stringify(shortcuts));
            renderShortcuts();
        }

        // Setup search input functionality
        function setupSearchInput() {
            const searchInput = document.getElementById('searchInput');

            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    googleSearch();
                }
            });

            // Focus on search input when page loads
            searchInput.focus();
        }

        // Search functions
        function googleSearch() {
            const query = document.getElementById('searchInput').value;
            if (query.trim()) {
                window.open(`https://www.google.com/search?q=${encodeURIComponent(query)}`, '_blank');
            }
        }

        function feelingLucky() {
            const query = document.getElementById('searchInput').value;
            if (query.trim()) {
                window.open(`https://www.google.com/search?q=${encodeURIComponent(query)}&btnI=1`, '_blank');
            } else {
                window.open('https://www.google.com/doodles', '_blank');
            }
        }

        // Voice search functionality
        function startVoiceSearch() {
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                const recognition = new SpeechRecognition();

                recognition.continuous = false;
                recognition.interimResults = false;
                recognition.lang = 'en-US';

                recognition.onstart = function() {
                    document.getElementById('searchInput').placeholder = 'Listening...';
                };

                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    document.getElementById('searchInput').value = transcript;
                    document.getElementById('searchInput').placeholder = '';
                    googleSearch();
                };

                recognition.onerror = function(event) {
                    document.getElementById('searchInput').placeholder = '';
                    alert('Voice search error: ' + event.error);
                };

                recognition.onend = function() {
                    document.getElementById('searchInput').placeholder = '';
                };

                recognition.start();
            } else {
                alert('Voice search is not supported in this browser.');
            }
        }

        // Image search functionality
        function openImageSearch() {
            window.open('https://images.google.com', '_blank');
        }

        // Header functions
        function showAbout() {
            window.open('https://about.google', '_blank');
        }

        function showStore() {
            window.open('https://store.google.com', '_blank');
        }

        function openGmail() {
            window.open('https://gmail.com', '_blank');
        }

        function openImages() {
            window.open('https://images.google.com', '_blank');
        }

        function toggleAppsMenu() {
            const dropdown = document.getElementById('appsMenuDropdown');
            const profileDropdown = document.getElementById('profileMenuDropdown');

            // Close profile menu if open
            profileDropdown.classList.remove('show');

            // Toggle apps menu
            dropdown.classList.toggle('show');
        }

        function toggleProfileMenu() {
            const dropdown = document.getElementById('profileMenuDropdown');
            const appsDropdown = document.getElementById('appsMenuDropdown');

            // Close apps menu if open
            appsDropdown.classList.remove('show');

            // Toggle profile menu
            dropdown.classList.toggle('show');
        }

        // Setup click outside to close dropdowns
        function setupClickOutside() {
            document.addEventListener('click', function(event) {
                const appsMenu = document.querySelector('.apps-menu');
                const profilePic = document.querySelector('.profile-pic');
                const appsDropdown = document.getElementById('appsMenuDropdown');
                const profileDropdown = document.getElementById('profileMenuDropdown');

                // Close apps dropdown if clicking outside
                if (!appsMenu.contains(event.target) && !appsDropdown.contains(event.target)) {
                    appsDropdown.classList.remove('show');
                }

                // Close profile dropdown if clicking outside
                if (!profilePic.contains(event.target) && !profileDropdown.contains(event.target)) {
                    profileDropdown.classList.remove('show');
                }
            });
        }

        // Profile menu functions
        function openAccountManagement() {
            window.open('https://myaccount.google.com', '_blank');
        }

        function addAccount() {
            window.open('https://accounts.google.com/signin/v2/identifier?continue=https%3A%2F%2Fmyaccount.google.com', '_blank');
        }

        function signOut() {
            if (confirm('Are you sure you want to sign out?')) {
                window.open('https://accounts.google.com/logout', '_blank');
            }
        }

        // Set random background (subtle)
        function setRandomBackground() {
            const backgrounds = [
                'linear-gradient(135deg, #202124 0%, #1a1a1d 100%)',
                'linear-gradient(135deg, #1e1e1e 0%, #2d2d30 100%)',
                'linear-gradient(135deg, #212121 0%, #1c1c1c 100%)'
            ];

            const randomBg = backgrounds[Math.floor(Math.random() * backgrounds.length)];
            document.body.style.background = randomBg;
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + K to focus search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                document.getElementById('searchInput').focus();
            }

            // Escape to clear search or close modal
            if (e.key === 'Escape') {
                const modal = document.getElementById('addShortcutModal');
                if (modal.style.display === 'block') {
                    closeAddShortcutModal();
                } else {
                    document.getElementById('searchInput').value = '';
                    document.getElementById('searchInput').focus();
                }
            }

            // Enter in modal to save
            if (e.key === 'Enter' && document.getElementById('addShortcutModal').style.display === 'block') {
                saveShortcut();
            }
        });

        // Close modal when clicking outside
        document.getElementById('addShortcutModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeAddShortcutModal();
            }
        });
    </script>
</body>
</html>